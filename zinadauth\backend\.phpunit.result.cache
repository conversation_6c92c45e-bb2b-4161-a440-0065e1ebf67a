{"version": 1, "defects": {"Tests\\Unit\\QuestionModelTest::it_can_create_a_code_question": 8, "Tests\\Unit\\QuestionModelTest::it_has_assignments_relationship": 8, "Tests\\Unit\\QuestionModelTest::it_can_filter_by_creator_scope": 7, "Tests\\Unit\\QuestionModelTest::it_calculates_difficulty_correctly": 7, "Tests\\Unit\\QuestionRepositoryTest::it_can_update_a_question": 8, "Tests\\Unit\\QuestionRepositoryTest::it_can_get_all_questions_with_pagination": 8, "Tests\\Unit\\QuestionRepositoryTest::it_can_search_questions_by_text": 8, "Tests\\Unit\\QuestionRepositoryTest::it_can_filter_questions_by_type": 8, "Tests\\Unit\\QuestionRepositoryTest::it_can_filter_questions_by_creator": 7, "Tests\\Unit\\QuestionRepositoryTest::it_can_filter_questions_by_points_range": 8, "Tests\\Unit\\QuestionRepositoryTest::it_can_filter_questions_by_duration_range": 8, "Tests\\Unit\\QuestionRepositoryTest::it_can_duplicate_a_question": 7, "Tests\\Unit\\QuestionRepositoryTest::it_can_bulk_delete_questions": 8, "Tests\\Unit\\QuestionRepositoryTest::it_validates_question_data_on_creation": 7, "Tests\\Unit\\QuestionRepositoryTest::it_validates_text_question_data": 7, "Tests\\Unit\\QuestionRepositoryTest::it_can_sort_questions": 8, "Tests\\Unit\\QuestionPolicyTest::admin_can_view_any_questions": 7, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_view_any_questions": 7, "Tests\\Unit\\QuestionPolicyTest::participant_can_view_any_questions": 7, "Tests\\Unit\\QuestionPolicyTest::admin_can_view_specific_question": 7, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_view_specific_question": 7, "Tests\\Unit\\QuestionPolicyTest::participant_can_view_specific_question": 7, "Tests\\Unit\\QuestionPolicyTest::admin_can_create_questions": 7, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_create_questions": 7, "Tests\\Unit\\QuestionPolicyTest::admin_can_update_any_question": 7, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_update_own_question": 8, "Tests\\Unit\\QuestionPolicyTest::admin_can_delete_any_question": 7, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_delete_own_question": 7, "Tests\\Unit\\QuestionPolicyTest::only_admin_can_bulk_create_questions": 7, "Tests\\Unit\\QuestionPolicyTest::only_admin_can_bulk_delete_questions": 7, "Tests\\Unit\\QuestionPolicyTest::admin_can_view_answers": 7, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_view_answers_for_own_questions": 7, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_view_answers_generally": 7, "Tests\\Unit\\QuestionPolicyTest::all_authenticated_users_can_search_questions": 7, "Tests\\Unit\\QuestionPolicyTest::admin_and_facilitator_can_duplicate_questions": 7, "Tests\\Unit\\QuestionPolicyTest::is_owner_or_admin_helper_works_correctly": 7, "Tests\\Unit\\QuestionPolicyTest::policy_handles_questions_created_by_different_users": 7, "Tests\\Unit\\QuestionRequestTest::it_allows_update_requests_with_sometimes_validation": 8, "Tests\\Feature\\QuestionControllerTest::authenticated_users_can_view_questions_list": 8, "Tests\\Feature\\QuestionControllerTest::admin_can_create_single_choice_question": 8, "Tests\\Feature\\QuestionControllerTest::facilitator_can_create_questions": 8, "Tests\\Feature\\QuestionControllerTest::participant_cannot_create_questions": 8, "Tests\\Feature\\QuestionControllerTest::it_validates_question_creation_data": 8, "Tests\\Feature\\QuestionControllerTest::authenticated_users_can_view_specific_question": 8, "Tests\\Feature\\QuestionControllerTest::it_returns_404_for_non_existent_question": 8, "Tests\\Feature\\QuestionControllerTest::facilitator_can_update_own_question": 8, "Tests\\Feature\\QuestionControllerTest::facilitator_cannot_update_others_question": 8, "Tests\\Feature\\QuestionControllerTest::admin_can_update_any_question": 8, "Tests\\Feature\\QuestionControllerTest::facilitator_can_delete_own_question": 8, "Tests\\Feature\\QuestionControllerTest::facilitator_cannot_delete_others_question": 8, "Tests\\Feature\\QuestionControllerTest::admin_can_delete_any_question": 8, "Tests\\Feature\\QuestionControllerTest::participant_cannot_delete_questions": 8, "Tests\\Feature\\QuestionControllerTest::authenticated_users_can_search_questions": 8, "Tests\\Feature\\QuestionControllerTest::users_can_filter_questions_by_type": 8, "Tests\\Feature\\QuestionControllerTest::users_can_get_random_questions_by_type": 8, "Tests\\Feature\\QuestionControllerTest::only_admin_can_bulk_create_questions": 8, "Tests\\Feature\\QuestionControllerTest::facilitator_cannot_bulk_create_questions": 8, "Tests\\Feature\\QuestionControllerTest::only_admin_can_bulk_delete_questions": 8, "Tests\\Feature\\QuestionControllerTest::facilitator_cannot_bulk_delete_questions": 8, "Tests\\Feature\\QuestionWorkflowTest::complete_question_management_workflow_for_facilitator": 7, "Tests\\Feature\\QuestionWorkflowTest::question_difficulty_calculation_workflow": 7, "Tests\\Feature\\QuestionWorkflowTest::authorization_workflow_across_different_user_types": 7, "Tests\\Feature\\QuestionControllerTest::unauthenticated_users_cannot_access_questions": 8}, "times": {"Tests\\Unit\\QuestionModelTest::it_can_create_a_single_choice_question": 0.034, "Tests\\Unit\\QuestionModelTest::it_can_create_a_multiple_choice_question": 0.006, "Tests\\Unit\\QuestionModelTest::it_can_create_a_text_question": 0.008, "Tests\\Unit\\QuestionModelTest::it_can_create_a_code_question": 0.008, "Tests\\Unit\\QuestionModelTest::it_has_creator_relationship": 0.033, "Tests\\Unit\\QuestionModelTest::it_has_assignments_relationship": 0.038, "Tests\\Unit\\QuestionModelTest::it_can_filter_by_type_scope": 0.025, "Tests\\Unit\\QuestionModelTest::it_can_filter_by_creator_scope": 0.02, "Tests\\Unit\\QuestionModelTest::it_can_filter_by_minimum_points_scope": 0.021, "Tests\\Unit\\QuestionModelTest::it_can_filter_by_maximum_duration_scope": 0.021, "Tests\\Unit\\QuestionModelTest::it_returns_question_text_based_on_language": 0.008, "Tests\\Unit\\QuestionModelTest::it_returns_choices_based_on_language": 0.008, "Tests\\Unit\\QuestionModelTest::it_calculates_difficulty_correctly": 0.024, "Tests\\Unit\\QuestionModelTest::it_hides_sensitive_attributes": 0.023, "Tests\\Unit\\QuestionModelTest::it_casts_attributes_correctly": 0.011, "Tests\\Unit\\QuestionModelTest::it_automatically_sets_created_by_on_creation": 0.011, "Tests\\Unit\\QuestionRepositoryTest::it_can_create_a_question": 0.011, "Tests\\Unit\\QuestionRepositoryTest::it_can_find_a_question_by_id": 0.012, "Tests\\Unit\\QuestionRepositoryTest::it_can_update_a_question": 0.014, "Tests\\Unit\\QuestionRepositoryTest::it_can_delete_a_question": 0.014, "Tests\\Unit\\QuestionRepositoryTest::it_can_get_all_questions_with_pagination": 0.144, "Tests\\Unit\\QuestionRepositoryTest::it_can_search_questions_by_text": 0.02, "Tests\\Unit\\QuestionRepositoryTest::it_can_filter_questions_by_type": 0.02, "Tests\\Unit\\QuestionRepositoryTest::it_can_filter_questions_by_creator": 0.028, "Tests\\Unit\\QuestionRepositoryTest::it_can_filter_questions_by_points_range": 0.02, "Tests\\Unit\\QuestionRepositoryTest::it_can_filter_questions_by_duration_range": 0.028, "Tests\\Unit\\QuestionRepositoryTest::it_can_get_questions_by_type": 0.034, "Tests\\Unit\\QuestionRepositoryTest::it_can_get_random_questions_by_type": 0.106, "Tests\\Unit\\QuestionRepositoryTest::it_can_duplicate_a_question": 0.016, "Tests\\Unit\\QuestionRepositoryTest::it_can_bulk_create_questions": 0.011, "Tests\\Unit\\QuestionRepositoryTest::it_can_bulk_delete_questions": 0.025, "Tests\\Unit\\QuestionRepositoryTest::it_can_get_statistics": 0.083, "Tests\\Unit\\QuestionRepositoryTest::it_validates_question_data_on_creation": 0.006, "Tests\\Unit\\QuestionRepositoryTest::it_validates_text_question_data": 0.004, "Tests\\Unit\\QuestionRepositoryTest::it_can_sort_questions": 0.032, "Tests\\Unit\\QuestionPolicyTest::admin_can_view_any_questions": 0.004, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_view_any_questions": 0.004, "Tests\\Unit\\QuestionPolicyTest::participant_can_view_any_questions": 0.005, "Tests\\Unit\\QuestionPolicyTest::admin_can_view_specific_question": 0.004, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_view_specific_question": 0.004, "Tests\\Unit\\QuestionPolicyTest::participant_can_view_specific_question": 0.003, "Tests\\Unit\\QuestionPolicyTest::admin_can_create_questions": 0.004, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_create_questions": 0.048, "Tests\\Unit\\QuestionPolicyTest::participant_cannot_create_questions": 0.012, "Tests\\Unit\\QuestionPolicyTest::admin_can_update_any_question": 0.004, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_update_own_question": 0.009, "Tests\\Unit\\QuestionPolicyTest::facilitator_cannot_update_others_question": 0.008, "Tests\\Unit\\QuestionPolicyTest::participant_cannot_update_questions": 0.003, "Tests\\Unit\\QuestionPolicyTest::admin_can_delete_any_question": 0.004, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_delete_own_question": 0.005, "Tests\\Unit\\QuestionPolicyTest::facilitator_cannot_delete_others_question": 0.007, "Tests\\Unit\\QuestionPolicyTest::participant_cannot_delete_questions": 0.004, "Tests\\Unit\\QuestionPolicyTest::only_admin_can_bulk_create_questions": 0.003, "Tests\\Unit\\QuestionPolicyTest::only_admin_can_bulk_delete_questions": 0.003, "Tests\\Unit\\QuestionPolicyTest::admin_can_view_answers": 0.003, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_view_answers_for_own_questions": 0.005, "Tests\\Unit\\QuestionPolicyTest::facilitator_cannot_view_answers_for_others_questions": 0.013, "Tests\\Unit\\QuestionPolicyTest::facilitator_can_view_answers_generally": 0.004, "Tests\\Unit\\QuestionPolicyTest::participant_cannot_view_answers": 0.004, "Tests\\Unit\\QuestionPolicyTest::all_authenticated_users_can_search_questions": 0.004, "Tests\\Unit\\QuestionPolicyTest::admin_and_facilitator_can_duplicate_questions": 0.004, "Tests\\Unit\\QuestionPolicyTest::participant_cannot_duplicate_questions": 0.003, "Tests\\Unit\\QuestionPolicyTest::is_owner_or_admin_helper_works_correctly": 0.006, "Tests\\Unit\\QuestionPolicyTest::policy_handles_questions_created_by_different_users": 0.022, "Tests\\Unit\\QuestionRequestTest::it_validates_required_fields_for_creation": 0.076, "Tests\\Unit\\QuestionRequestTest::it_validates_question_text_length": 0.03, "Tests\\Unit\\QuestionRequestTest::it_validates_question_type": 0.016, "Tests\\Unit\\QuestionRequestTest::it_validates_points_range": 0.016, "Tests\\Unit\\QuestionRequestTest::it_validates_duration_range": 0.018, "Tests\\Unit\\QuestionRequestTest::it_validates_choices_for_choice_based_questions": 0.015, "Tests\\Unit\\QuestionRequestTest::it_validates_minimum_choices_count": 0.015, "Tests\\Unit\\QuestionRequestTest::it_validates_maximum_choices_count": 0.021, "Tests\\Unit\\QuestionRequestTest::it_validates_choice_text_length": 0.01, "Tests\\Unit\\QuestionRequestTest::it_validates_text_answer_for_text_questions": 0.017, "Tests\\Unit\\QuestionRequestTest::it_validates_text_answer_length": 0.018, "Tests\\Unit\\QuestionRequestTest::it_validates_answer_array_format": 0.012, "Tests\\Unit\\QuestionRequestTest::it_validates_arabic_text_fields": 0.012, "Tests\\Unit\\QuestionRequestTest::it_allows_update_requests_with_sometimes_validation": 0.004, "Tests\\Feature\\QuestionControllerTest::authenticated_users_can_view_questions_list": 0.393, "Tests\\Feature\\QuestionControllerTest::unauthenticated_users_cannot_access_questions": 0.019, "Tests\\Feature\\QuestionControllerTest::admin_can_create_single_choice_question": 0.066, "Tests\\Feature\\QuestionControllerTest::facilitator_can_create_questions": 0.028, "Tests\\Feature\\QuestionControllerTest::participant_cannot_create_questions": 0.023, "Tests\\Feature\\QuestionControllerTest::it_validates_question_creation_data": 0.082, "Tests\\Feature\\QuestionControllerTest::authenticated_users_can_view_specific_question": 0.063, "Tests\\Feature\\QuestionControllerTest::it_returns_404_for_non_existent_question": 0.02, "Tests\\Feature\\QuestionControllerTest::facilitator_can_update_own_question": 0.077, "Tests\\Feature\\QuestionControllerTest::facilitator_cannot_update_others_question": 0.032, "Tests\\Feature\\QuestionControllerTest::admin_can_update_any_question": 0.047, "Tests\\Feature\\QuestionControllerTest::facilitator_can_delete_own_question": 0.047, "Tests\\Feature\\QuestionControllerTest::facilitator_cannot_delete_others_question": 0.048, "Tests\\Feature\\QuestionControllerTest::admin_can_delete_any_question": 0.042, "Tests\\Feature\\QuestionControllerTest::participant_cannot_delete_questions": 0.027, "Tests\\Feature\\QuestionControllerTest::authenticated_users_can_search_questions": 0.047, "Tests\\Feature\\QuestionControllerTest::users_can_filter_questions_by_type": 0.063, "Tests\\Feature\\QuestionControllerTest::users_can_get_random_questions_by_type": 0.162, "Tests\\Feature\\QuestionControllerTest::only_admin_can_bulk_create_questions": 0.05, "Tests\\Feature\\QuestionControllerTest::facilitator_cannot_bulk_create_questions": 0.025, "Tests\\Feature\\QuestionControllerTest::only_admin_can_bulk_delete_questions": 0.08, "Tests\\Feature\\QuestionControllerTest::facilitator_cannot_bulk_delete_questions": 0.033, "Tests\\Feature\\QuestionWorkflowTest::complete_question_management_workflow_for_facilitator": 0.203, "Tests\\Feature\\QuestionWorkflowTest::admin_can_perform_bulk_operations": 0.106, "Tests\\Feature\\QuestionWorkflowTest::multilingual_question_workflow": 0.038, "Tests\\Feature\\QuestionWorkflowTest::question_assignment_integration": 0.055, "Tests\\Feature\\QuestionWorkflowTest::question_difficulty_calculation_workflow": 0.078, "Tests\\Feature\\QuestionWorkflowTest::authorization_workflow_across_different_user_types": 0.365}}