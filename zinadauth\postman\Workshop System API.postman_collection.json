{"info": {"_postman_id": "d82a3246-d1c7-4241-8144-cf92f5093a11", "name": "Workshop System API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "46935019", "_collection_link": "https://zinad-team.postman.co/workspace/Team-Workspace~7407359a-0139-4836-86f4-cf7503c22321/collection/46935019-d82a3246-d1c7-4241-8144-cf92f5093a11?action=share&source=collection_link&creator=46935019"}, "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Pass23ord@123\",\n    \"password_confirmation\": \"Pass23ord@123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/auth/register", "host": ["{{baseURL}}"], "path": ["api", "auth", "register"]}}, "response": [{"name": "Validation Error", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "<PERSON>", "description": "required, string, max:255, min:2", "type": "text"}, {"key": "email", "value": "<EMAIL>", "description": "required, string, max:255", "type": "text"}, {"key": "password", "value": "Password@123", "description": "required, letters, numbers, symbols", "type": "text"}, {"key": "password_confirmation", "value": "Password@123", "description": "required, matches the password field", "type": "text"}]}, "url": {"raw": "{{baseURL}}/api/register", "host": ["{{baseURL}}"], "path": ["api", "register"]}}, "status": "Unprocessable Content", "code": 422, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 21 Jul 2025 13:27:21 GMT"}, {"key": "X-RateLimit-Limit", "value": "3"}, {"key": "X-RateLimit-Remaining", "value": "2"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"message\": \"Validation failed\",\n    \"errors\": {\n        \"password\": [\n            \"The given password has appeared in a data leak. Please choose a different password.\"\n        ]\n    }\n}"}, {"name": "Success", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "<PERSON>", "description": "required, string, max:255, min:2", "type": "text"}, {"key": "email", "value": "<EMAIL>", "description": "required, string, max:255", "type": "text"}, {"key": "password", "value": "Pass23ord@123", "description": "required, letters, numbers, symbols", "type": "text"}, {"key": "password_confirmation", "value": "Pass23ord@123", "description": "required, matches the password field", "type": "text"}]}, "url": {"raw": "{{baseURL}}/api/register", "host": ["{{baseURL}}"], "path": ["api", "register"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 21 Jul 2025 13:27:52 GMT"}, {"key": "X-RateLimit-Limit", "value": "3"}, {"key": "X-RateLimit-Remaining", "value": "1"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"message\": \"User created successfully\",\n    \"data\": {\n        \"id\": 2,\n        \"name\": \"<PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"created_at\": \"2025-07-21 13:27:52\"\n    }\n}"}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Pass23ord@123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/auth/login", "host": ["{{baseURL}}"], "path": ["api", "auth", "login"]}}, "response": [{"name": "Invalid Credentials", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "password123", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/login", "host": ["{{baseUrl}}"], "path": ["api", "login"]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 21 Jul 2025 13:19:23 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"message\": \"Invalid credentials\",\n    \"errors\": []\n}"}, {"name": "Success", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "Pass23ord@123", "type": "text"}]}, "url": {"raw": "{{baseURL}}/api/login", "host": ["{{baseURL}}"], "path": ["api", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 21 Jul 2025 13:28:22 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"message\": \"Login successful\",\n    \"data\": {\n        \"token\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDAvYXBpL2xvZ2luIiwiaWF0IjoxNzUzMTA0NTAyLCJleHAiOjE3NTMxMDgxMDIsIm5iZiI6MTc1MzEwNDUwMiwianRpIjoiMmx0MVZiOW5TTko4VU9BZyIsInN1YiI6IjIiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.pt2UFKP4RMzefcHBL5r4Q_BR1eNNv2g3xc-1RFF8D_w\",\n        \"user\": {\n            \"id\": 2,\n            \"name\": \"<PERSON>\",\n            \"email\": \"<EMAIL>\",\n            \"created_at\": \"2025-07-21 13:27:52\"\n        }\n    }\n}"}]}, {"name": "Logout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseURL}}/api/auth/logout", "host": ["{{baseURL}}"], "path": ["api", "auth", "logout"]}}, "response": [{"name": "Success", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseURL}}/api/logout", "host": ["{{baseURL}}"], "path": ["api", "logout"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 21 Jul 2025 13:29:31 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"message\": \"User logged out successfully\",\n    \"data\": null\n}"}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": ["var response = pm.response.json();", "", "pm.environment.set('token', response.data?.token);"]}}]}, {"name": "Questions", "item": [{"name": "getquestions", "request": {"method": "GET", "header": []}, "response": []}, {"name": "create_questions", "request": {"method": "GET", "header": []}, "response": []}, {"name": "get_question_by_id", "request": {"method": "GET", "header": []}, "response": []}, {"name": "update_question", "request": {"method": "GET", "header": []}, "response": []}, {"name": "delete_question", "request": {"method": "GET", "header": []}, "response": []}, {"name": "get_sin_choice", "request": {"method": "GET", "header": []}, "response": []}, {"name": "get_random_single", "request": {"method": "GET", "header": []}, "response": []}, {"name": "duplicate_question", "request": {"method": "GET", "header": []}, "response": []}, {"name": "Question Statistics", "request": {"method": "GET", "header": []}, "response": []}, {"name": "Search Questions", "request": {"method": "GET", "header": []}, "response": []}, {"name": "Bulk Create Questions", "request": {"method": "GET", "header": []}, "response": []}, {"name": "Delete Bulk", "request": {"method": "GET", "header": []}, "response": []}]}, {"name": "Workshops", "item": [{"name": "Get Workshops", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/workshops", "host": ["{{baseURL}}"], "path": ["api", "workshops"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/workshops", "host": ["{{baseURL}}"], "path": ["api", "workshops"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:30:03 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Records retrieved successfully\",\n    \"data\": [\n        {\n            \"id\": \"01984759-63e8-7322-b6aa-24a570f10d52\",\n            \"title\": \"This is a title\",\n            \"description\": \"This is a description\",\n            \"start_at\": \"2025-12-23\",\n            \"end_at\": \"2025-12-25\",\n            \"status\": \"active\",\n            \"qr_status\": false,\n            \"pin_code\": 123455,\n            \"setting_id\": null,\n            \"created_by\": {\n                \"id\": \"01984758-9726-7335-8045-425f8c4ea129\",\n                \"name\": \"<PERSON>\"\n            },\n            \"created_at\": \"2025-07-26T15:28:17.000000Z\"\n        }\n    ],\n    \"pagination\": {\n        \"current_page\": 1,\n        \"last_page\": 1,\n        \"per_page\": 15,\n        \"total\": 1,\n        \"from\": 1,\n        \"to\": 1\n    }\n}"}]}, {"name": "Get Workshop by Id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/workshops/01984792-cac5-7021-8bf8-6737eaeb005", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984792-cac5-7021-8bf8-6737eaeb005"]}}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/workshops/01984792-cac5-7021-8bf8-6737eae7b005", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984792-cac5-7021-8bf8-6737eae7b005"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:38:54 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Record found successfully\",\n    \"data\": {\n        \"id\": \"01984792-cac5-7021-8bf8-6737eae7b005\",\n        \"title\": \"This is a title\",\n        \"description\": \"This is a description\",\n        \"start_at\": \"2025-12-23\",\n        \"end_at\": \"2025-12-25\",\n        \"status\": \"active\",\n        \"qr_status\": false,\n        \"pin_code\": 123458,\n        \"setting_id\": null,\n        \"created_by\": {\n            \"id\": \"01984758-9726-7335-8045-425f8c4ea129\",\n            \"name\": \"<PERSON>\"\n        },\n        \"created_at\": \"2025-07-26T16:30:59.000000Z\"\n    }\n}"}, {"name": "404", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/workshops/01984792-cac5-7021-8bf8-6737eaeb005", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984792-cac5-7021-8bf8-6737eaeb005"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:39:07 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Record not found\"\n}"}]}, {"name": "Store Workshop", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\" : \"This is a title\",\n    \"description\": \"This is a description\",\n    \"start_at\": \"2025-12-23\",\n    \"end_at\": \"2025-12-25\",\n    \"pin_code\": \"123455\",\n    \"qr_status\": 0,\n    \"status\": \"active\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/workshops", "host": ["{{baseURL}}"], "path": ["api", "workshops"]}}, "response": [{"name": "Success", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\" : \"This is a title\",\n    \"description\": \"This is a description\",\n    \"start_at\": \"2025-12-23\",\n    \"end_at\": \"2025-12-25\",\n    \"pin_code\": \"123455\",\n    \"qr_status\": 0,\n    \"status\": \"active\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/workshops", "host": ["{{baseURL}}"], "path": ["api", "workshops"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sun, 27 Jul 2025 07:48:52 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Record created successfully\",\n    \"data\": {\n        \"id\": \"01984adb-2641-700b-a6d0-1a4b7d536767\",\n        \"title\": \"This is a title\",\n        \"description\": \"This is a description\",\n        \"start_at\": \"2025-12-23\",\n        \"end_at\": \"2025-12-25\",\n        \"status\": \"active\",\n        \"qr_status\": false,\n        \"pin_code\": \"123455\",\n        \"setting_id\": null,\n        \"created_by\": {\n            \"id\": \"01984ac5-8bd8-724b-9f43-bcab5d641477\",\n            \"name\": \"<PERSON>\"\n        },\n        \"created_at\": \"2025-07-27T07:48:52.000000Z\"\n    }\n}"}, {"name": "Unauthorized", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\" : \"This is a title\",\n    \"description\": \"This is a description\",\n    \"start_at\": \"2025-12-23\",\n    \"end_at\": \"2025-12-25\",\n    \"pin_code\": \"123455\",\n    \"qr_status\": 0,\n    \"status\": \"active\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/workshops", "host": ["{{baseURL}}"], "path": ["api", "workshops"]}}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sun, 27 Jul 2025 07:46:19 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"message\": \"Unauthorized access\",\n    \"errors\": []\n}"}]}, {"name": "Update Workshop", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\" : \"This is a title Updated\"\n    // \"description\": \"This is a description\",\n    // \"start_at\": \"2025-12-23\",\n    // \"end_at\": \"2025-12-25\",\n    // \"pin_code\": \"123455\",\n    // \"qr_status\": 0,\n    // \"status\": \"active\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/workshops/01984792-cac5-7021-8bf8-6737eae7b005", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984792-cac5-7021-8bf8-6737eae7b005"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\" : \"This is a title Updated\"\n    // \"description\": \"This is a description\",\n    // \"start_at\": \"2025-12-23\",\n    // \"end_at\": \"2025-12-25\",\n    // \"pin_code\": \"123455\",\n    // \"qr_status\": 0,\n    // \"status\": \"active\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/workshops/01984759-63e8-7322-b6aa-24a570f10d52", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984759-63e8-7322-b6aa-24a570f10d52"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:35:52 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Record updated successfully\",\n    \"data\": {\n        \"id\": \"01984759-63e8-7322-b6aa-24a570f10d52\",\n        \"title\": \"This is a title Updated\",\n        \"description\": \"This is a description\",\n        \"start_at\": \"2025-12-23\",\n        \"end_at\": \"2025-12-25\",\n        \"status\": \"active\",\n        \"qr_status\": false,\n        \"pin_code\": 123455,\n        \"setting_id\": null,\n        \"created_by\": {\n            \"id\": \"01984758-9726-7335-8045-425f8c4ea129\",\n            \"name\": \"<PERSON>\"\n        },\n        \"created_at\": \"2025-07-26T15:28:17.000000Z\"\n    }\n}"}]}, {"name": "Delete Workshop", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseURL}}/api/workshops/01984759-63e8-7322-b6aa-24a570f10d52", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984759-63e8-7322-b6aa-24a570f10d52"]}}, "response": [{"name": "Success", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseURL}}/api/workshops/01984759-63e8-7322-b6aa-24a570f10d52", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984759-63e8-7322-b6aa-24a570f10d52"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:37:20 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Record deleted successfully\"\n}"}, {"name": "Workshop not found", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseURL}}/api/workshops/01984759-63e8-7322-b6aa-24a570f10d52", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984759-63e8-7322-b6aa-24a570f10d52"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:37:28 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Record not found\"\n}"}]}, {"name": "Store Workshop Settings", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "client", "value": "This is a client Text", "type": "text"}, {"key": "logo", "type": "file", "src": "/home/<USER>/Downloads/test.jpeg"}, {"key": "primary_color", "value": "#4287f5", "type": "text"}, {"key": "secondary_color", "value": "#dedfe0", "type": "text"}, {"key": "dark_primary_color", "value": "#0c1d38", "type": "text"}, {"key": "dark_secondary_color", "value": "#464747", "type": "text"}, {"key": "lang", "value": "en", "type": "text"}, {"key": "domain_name", "value": "zinad.net", "type": "text"}, {"key": "duration", "value": "26", "type": "text"}]}, "url": {"raw": "{{baseURL}}/api/workshops/01985007-ae83-70b9-a6ef-ce7d9e4afa62/settings", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01985007-ae83-70b9-a6ef-ce7d9e4afa62", "settings"]}}, "response": [{"name": "Success", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "client", "value": "This is a client Text", "type": "text"}, {"key": "logo", "type": "file", "src": "/home/<USER>/Downloads/test.jpeg"}, {"key": "primary_color", "value": "#4287f5", "type": "text"}, {"key": "secondary_color", "value": "#dedfe0", "type": "text"}, {"key": "dark_primary_color", "value": "#0c1d38", "type": "text"}, {"key": "dark_secondary_color", "value": "#464747", "type": "text"}, {"key": "lang", "value": "en", "type": "text"}, {"key": "domain_name", "value": "zinad.net", "type": "text"}, {"key": "duration", "value": "26", "type": "text"}], "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/workshops/01985007-ae83-70b9-a6ef-ce7d9e4afa62/settings", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01985007-ae83-70b9-a6ef-ce7d9e4afa62", "settings"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 28 Jul 2025 08:59:19 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"message\": \"Setting created successfully.\",\n    \"data\": {\n        \"id\": \"01985041-ffd9-71d1-b8da-a07e132da362\",\n        \"client\": \"This is a client Text\",\n        \"logo\": \"http://localhost:8000/workshop/logo/mpuHg09Eh3FQqNNc3vREkaUrPOOSmDaqWTBtZ1Wu.jpg\",\n        \"primary_color\": \"#4287f5\",\n        \"secondary_color\": \"#dedfe0\",\n        \"dark_primary_color\": \"#0c1d38\",\n        \"dark_secondary_color\": \"#464747\",\n        \"lang\": \"en\",\n        \"domain_name\": \"zinad.net\",\n        \"duration\": \"26\",\n        \"created_at\": \"2025-07-28T08:59:19.000000Z\"\n    }\n}"}]}]}, {"name": "Assignments", "item": [{"name": "Get Assingnments", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/assignments", "host": ["{{baseURL}}"], "path": ["api", "assignments"]}}, "response": []}, {"name": "Get Assignemnts by Id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/workshops/01984792-cac5-7021-8bf8-6737eaeb005", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984792-cac5-7021-8bf8-6737eaeb005"]}}, "response": [{"name": "404", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/workshops/01984792-cac5-7021-8bf8-6737eaeb005", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984792-cac5-7021-8bf8-6737eaeb005"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:39:07 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Record not found\"\n}"}]}, {"name": "Store Assignments", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\" : \"This is a title\",\n    \"description\": \"This is a description\",\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/assignments", "host": ["{{baseURL}}"], "path": ["api", "assignments"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\" : \"This is a title\",\n    \"description\": \"This is a description\",\n    \"start_at\": \"2025-12-23\",\n    \"end_at\": \"2025-12-25\",\n    \"pin_code\": \"123458\",\n    \"qr_status\": 0,\n    \"status\": \"active\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/workshops", "host": ["{{baseURL}}"], "path": ["api", "workshops"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:30:59 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Record created successfully\",\n    \"data\": {\n        \"id\": \"01984792-cac5-7021-8bf8-6737eae7b005\",\n        \"title\": \"This is a title\",\n        \"description\": \"This is a description\",\n        \"start_at\": \"2025-12-23\",\n        \"end_at\": \"2025-12-25\",\n        \"status\": \"active\",\n        \"qr_status\": false,\n        \"pin_code\": \"123458\",\n        \"setting_id\": null,\n        \"created_by\": {\n            \"id\": \"01984758-9726-7335-8045-425f8c4ea129\",\n            \"name\": \"<PERSON>\"\n        },\n        \"created_at\": \"2025-07-26T16:30:59.000000Z\"\n    }\n}"}, {"name": "Validation Error", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\" : \"This is a title\",\n    \"description\": \"This is a description\",\n    \"start_at\": \"2025-12-23\",\n    \"end_at\": \"2025-12-25\",\n    \"pin_code\": \"123455\",\n    \"qr_status\": 0,\n    \"status\": \"active\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/workshops", "host": ["{{baseURL}}"], "path": ["api", "workshops"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:30:08 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Failed to create record\",\n    \"error\": \"The pin code has already been taken.\"\n}"}]}, {"name": "Update Assignments", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\" : \"This is a title Updated\"\n    // \"description\": \"This is a description\",\n    // \"start_at\": \"2025-12-23\",\n    // \"end_at\": \"2025-12-25\",\n    // \"pin_code\": \"123455\",\n    // \"qr_status\": 0,\n    // \"status\": \"active\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/workshops/01984792-cac5-7021-8bf8-6737eae7b005", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984792-cac5-7021-8bf8-6737eae7b005"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\" : \"This is a title Updated\"\n    // \"description\": \"This is a description\",\n    // \"start_at\": \"2025-12-23\",\n    // \"end_at\": \"2025-12-25\",\n    // \"pin_code\": \"123455\",\n    // \"qr_status\": 0,\n    // \"status\": \"active\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/workshops/01984759-63e8-7322-b6aa-24a570f10d52", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984759-63e8-7322-b6aa-24a570f10d52"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:35:52 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Record updated successfully\",\n    \"data\": {\n        \"id\": \"01984759-63e8-7322-b6aa-24a570f10d52\",\n        \"title\": \"This is a title Updated\",\n        \"description\": \"This is a description\",\n        \"start_at\": \"2025-12-23\",\n        \"end_at\": \"2025-12-25\",\n        \"status\": \"active\",\n        \"qr_status\": false,\n        \"pin_code\": 123455,\n        \"setting_id\": null,\n        \"created_by\": {\n            \"id\": \"01984758-9726-7335-8045-425f8c4ea129\",\n            \"name\": \"<PERSON>\"\n        },\n        \"created_at\": \"2025-07-26T15:28:17.000000Z\"\n    }\n}"}]}, {"name": "Delete Assignments", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseURL}}/api/assignments/01984759-63e8-7322-b6aa-24a570f10d52", "host": ["{{baseURL}}"], "path": ["api", "assignments", "01984759-63e8-7322-b6aa-24a570f10d52"]}}, "response": [{"name": "Success", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseURL}}/api/workshops/01984759-63e8-7322-b6aa-24a570f10d52", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984759-63e8-7322-b6aa-24a570f10d52"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:37:20 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Record deleted successfully\"\n}"}, {"name": "Workshop not found", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseURL}}/api/workshops/01984759-63e8-7322-b6aa-24a570f10d52", "host": ["{{baseURL}}"], "path": ["api", "workshops", "01984759-63e8-7322-b6aa-24a570f10d52"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Sat, 26 Jul 2025 16:37:28 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Record not found\"\n}"}]}]}, {"name": "Template", "item": [{"name": "Get Templates", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/templates", "host": ["{{baseURL}}"], "path": ["api", "templates"]}}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/templates", "host": ["{{baseURL}}"], "path": ["api", "templates"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 28 Jul 2025 16:59:54 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Records retrieved successfully\",\n    \"data\": [\n        {\n            \"id\": \"019851f8-6751-7307-899c-86ff92575667\",\n            \"title\": \"This is a title\",\n            \"description\": \"This is a description\",\n            \"setting_id\": \"019851f7-b1a0-71a8-b12b-c8110d9a1ca2\",\n            \"created_at\": \"2025-07-28T16:58:10.000000Z\"\n        }\n    ],\n    \"pagination\": {\n        \"current_page\": 1,\n        \"last_page\": 1,\n        \"per_page\": 15,\n        \"total\": 1,\n        \"from\": 1,\n        \"to\": 1\n    }\n}"}]}, {"name": "Get Template by Id", "request": {"method": "GET", "header": []}, "response": [{"name": "Success", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseURL}}/api/templates/019851f8-6751-7307-899c-86ff92575667", "host": ["{{baseURL}}"], "path": ["api", "templates", "019851f8-6751-7307-899c-86ff92575667"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 28 Jul 2025 16:59:39 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Record found successfully\",\n    \"data\": {\n        \"id\": \"019851f8-6751-7307-899c-86ff92575667\",\n        \"title\": \"This is a title\",\n        \"description\": \"This is a description\",\n        \"setting_id\": \"019851f7-b1a0-71a8-b12b-c8110d9a1ca2\",\n        \"created_at\": \"2025-07-28T16:58:10.000000Z\"\n    }\n}"}]}, {"name": "Store Template for an entity", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\": \"This is a title\",\n    \"title_ar\": \"هذا هو العنوان\",\n    \"description\": \"This is a description\",\n    \"description_ar\": \"هذا هو الوصف\",\n    \"setting_id\": \"019851f7-b1a0-71a8-b12b-c8110d9a1ca2\",\n    \"linkable_type\": \"workshop\",\n    \"linkable_id\": \"019851f7-5284-7250-8880-a219cd26ebfa\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/templates", "host": ["{{baseURL}}"], "path": ["api", "templates"]}}, "response": [{"name": "Success", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\": \"This is a title\",\n    \"title_ar\": \"هذا هو العنوان\",\n    \"description\": \"This is a description\",\n    \"description_ar\": \"هذا هو الوصف\",\n    \"setting_id\": \"019851f7-b1a0-71a8-b12b-c8110d9a1ca2\",\n    \"linkable_type\": \"workshop\",\n    \"linkable_id\": \"019851f7-5284-7250-8880-a219cd26ebfa\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/templates", "host": ["{{baseURL}}"], "path": ["api", "templates"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 28 Jul 2025 20:19:29 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"message\": \"Template created successfully.\",\n    \"data\": {\n        \"id\": \"019852b0-b544-72d6-a82c-7c63c69f71e9\",\n        \"title\": \"This is a title\",\n        \"description\": \"This is a description\",\n        \"setting_id\": \"019851f7-b1a0-71a8-b12b-c8110d9a1ca2\",\n        \"linkable\": {\n            \"id\": \"019851f7-5284-7250-8880-a219cd26ebfa\",\n            \"title\": \"This is a third title\",\n            \"description\": \"This is a third description\",\n            \"start_at\": \"2025-12-23T00:00:00.000000Z\",\n            \"end_at\": \"2025-12-25T00:00:00.000000Z\",\n            \"created_by\": \"019851ea-682c-7297-9072-54e0220fb434\",\n            \"setting_id\": \"019851f7-b1a0-71a8-b12b-c8110d9a1ca2\",\n            \"is_deleted\": false,\n            \"qr_status\": false,\n            \"status\": \"active\",\n            \"pin_code\": 793894,\n            \"created_at\": \"2025-07-28T16:56:59.000000Z\",\n            \"updated_at\": \"2025-07-28T16:57:24.000000Z\"\n        },\n        \"created_at\": \"2025-07-28T20:19:29.000000Z\"\n    }\n}"}]}, {"name": "Update Template", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\": \"This is a title edited\"\n    // \"title_ar\": \"هذا هو العنوان\",\n    // \"description\": \"This is a description\",\n    // \"description_ar\": \"هذا هو الوصف\",\n    // \"setting_id\": \"019851f7-b1a0-71a8-b12b-c8110d9a1ca2\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/templates/019851f8-6751-7307-899c-86ff92575667", "host": ["{{baseURL}}"], "path": ["api", "templates", "019851f8-6751-7307-899c-86ff92575667"]}}, "response": [{"name": "Success", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"title\": \"This is a title edited\"\n    // \"title_ar\": \"هذا هو العنوان\",\n    // \"description\": \"This is a description\",\n    // \"description_ar\": \"هذا هو الوصف\",\n    // \"setting_id\": \"019851f7-b1a0-71a8-b12b-c8110d9a1ca2\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/api/templates/019851f8-6751-7307-899c-86ff92575667", "host": ["{{baseURL}}"], "path": ["api", "templates", "019851f8-6751-7307-899c-86ff92575667"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 28 Jul 2025 17:02:20 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Record updated successfully\",\n    \"data\": {\n        \"id\": \"019851f8-6751-7307-899c-86ff92575667\",\n        \"title\": \"This is a title edited\",\n        \"description\": \"This is a description\",\n        \"setting_id\": \"019851f7-b1a0-71a8-b12b-c8110d9a1ca2\",\n        \"created_at\": \"2025-07-28T16:58:10.000000Z\"\n    }\n}"}]}, {"name": "Delete Template", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseURL}}/api/templates/019851f8-6751-7307-899c-86ff92575667", "host": ["{{baseURL}}"], "path": ["api", "templates", "019851f8-6751-7307-899c-86ff92575667"]}}, "response": [{"name": "Success", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseURL}}/api/templates/019851f8-6751-7307-899c-86ff92575667", "host": ["{{baseURL}}"], "path": ["api", "templates", "019851f8-6751-7307-899c-86ff92575667"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Server", "value": "nginx/1.29.0"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Powered-By", "value": "PHP/8.2.29"}, {"key": "Cache-Control", "value": "no-cache, private"}, {"key": "Date", "value": "Mon, 28 Jul 2025 17:03:09 GMT"}, {"key": "Access-Control-Allow-Origin", "value": "*"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Record deleted successfully\"\n}"}]}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": ["pm.request.addHeader(\"Accept: application/json\");"]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "baseURL", "value": "{{baseURL}", "type": "default"}]}