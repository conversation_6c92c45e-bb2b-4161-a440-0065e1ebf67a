Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD27AC0000 ntdll.dll
7FFD26240000 KERNEL32.DLL
7FFD24D40000 KERNELBASE.dll
7FFD26500000 USER32.dll
000210040000 msys-2.0.dll
7FFD254A0000 win32u.dll
7FFD26D50000 GDI32.dll
7FFD252C0000 gdi32full.dll
7FFD25560000 msvcp_win.dll
7FFD24BF0000 ucrtbase.dll
7FFD25850000 advapi32.dll
7FFD25DB0000 msvcrt.dll
7FFD25D00000 sechost.dll
7FFD26010000 RPCRT4.dll
7FFD240E0000 CRYPTBASE.DLL
7FFD25400000 bcryptPrimitives.dll
7FFD25E60000 IMM32.DLL
